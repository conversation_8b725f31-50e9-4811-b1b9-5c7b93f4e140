services:
  memory-mcp:
    build: 
      context: api/
      dockerfile: Dockerfile
    container_name: memory-mcp
    restart: unless-stopped
    env_file:
      - api/.env
    environment:
      # Override networking for cross-platform compatibility
      - QDRANT_HOST=${QDRANT_HOST:-host.docker.internal}
      - SUPABASE_HOST=${SUPABASE_HOST:-host.docker.internal}
    ports:
      - "8765:8765"
    volumes:
      - ./api:/usr/src/app
      - api_node_modules:/usr/src/app/node_modules
    working_dir: /usr/src/app
    command: >
      sh -c "uvicorn main:app --host 0.0.0.0 --port 8765 --reload --workers 4"
    extra_hosts:
      # Cross-platform host resolution
      - "qdrant.local:${EXTERNAL_QDRANT_IP:-host-gateway}"
      - "supabase.local:${EXTERNAL_SUPABASE_IP:-host-gateway}"
    networks:
      - memory-network
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '1.0'
          
  memory-ui:
    build:
      context: memory-ui/
      dockerfile: Dockerfile
    container_name: memory-ui
    restart: unless-stopped
    ports:
      - "3210:3210"
    env_file:
      - memory-ui/.env.local
    environment:
      # API endpoint configuration for cross-platform
      - NEXT_PUBLIC_API_URL=${API_URL:-http://localhost:8765}
    volumes:
      - ./memory-ui:/usr/src/app
      - ui_node_modules:/usr/src/app/node_modules
      - ui_next_cache:/usr/src/app/.next
    working_dir: /usr/src/app
    depends_on:
      - memory-mcp
    networks:
      - memory-network
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'

volumes:
  api_node_modules:
    driver: local
  ui_node_modules:
    driver: local
  ui_next_cache:
    driver: local

networks:
  memory-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16