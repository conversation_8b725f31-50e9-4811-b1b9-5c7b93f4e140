"use client"

import Link from "next/link"
import { usePathname } from "next/navigation"
import { cn } from "@/lib/utils"
import {
  Home,
  RefreshCw,
  Wrench,
  Settings,
  LogOut,
  User,
  ChevronRight,
  Wifi,
  WifiOff,
  Brain,
} from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { useAppSelector } from "@/store/hooks"
import { PresenceIndicator } from "@/components/PresenceIndicator"
import { createClient } from "@/lib/supabase/client"
import { useRouter } from "next/navigation"

const navigation = [
  { name: "Dashboard", href: "/", icon: Home },
  { name: "Maintenance", href: "/maintenance", icon: Wrench },
  { name: "Settings", href: "/settings", icon: Settings },
]

interface SidebarProps {
  isCollapsed?: boolean
}

export default function Sidebar({ isCollapsed = false }: SidebarProps) {
  const pathname = usePathname()
  const router = useRouter()
  const { userId, displayName, isAuthenticated } = useAppSelector(state => state.profile)
  const { connectionState } = useAppSelector(state => state.evolution)
  const supabase = createClient()
  
  const getInitials = (name: string) => {
    return name.split(' ').map(n => n[0]).join('').toUpperCase()
  }

  const handleSignOut = async () => {
    await supabase.auth.signOut()
    router.push('/login')
  }

  return (
    <div className={cn(
      "flex h-full flex-col border-r border-border/50 bg-card/30 backdrop-blur-xl transition-all duration-300",
      isCollapsed ? "w-16" : "w-64"
    )}>
      {/* Logo */}
      <div className="flex h-16 items-center justify-between px-3 border-b border-border/50">
        <div className="flex items-center min-w-0">
          <Brain className="h-8 w-8 text-primary flex-shrink-0" />
          {!isCollapsed && (
            <span className="ml-2 text-xl font-bold gradient-text truncate">Memory Master</span>
          )}
        </div>
        <div className="flex items-center gap-2">
          {!isCollapsed && (
            <>
              {connectionState === 'connected' ? (
                <Wifi className="h-4 w-4 text-firebase-yellow" />
              ) : connectionState === 'connecting' ? (
                <Wifi className="h-4 w-4 text-firebase-yellow animate-pulse" />
              ) : (
                <WifiOff className="h-4 w-4 text-firebase-red" />
              )}
            </>
          )}
        </div>
      </div>

      {/* Navigation */}
      <nav className="flex-1 space-y-1 px-3 py-4">
        {navigation.map((item) => {
          const isActive = pathname === item.href
          return (
            <Link
              key={item.name}
              href={item.href}
              className={cn(
                "group flex items-center px-3 py-2 text-sm font-medium rounded-md transition-all duration-200 relative",
                isActive
                  ? "bg-primary/10 text-primary glow"
                  : "text-muted-foreground hover:bg-accent/50 hover:text-accent-foreground",
                isCollapsed ? "justify-center" : ""
              )}
              title={isCollapsed ? item.name : undefined}
            >
              <item.icon
                className={cn(
                  "h-5 w-5 flex-shrink-0 transition-colors",
                  isActive ? "text-primary" : "text-muted-foreground group-hover:text-accent-foreground",
                  isCollapsed ? "mr-0" : "mr-3"
                )}
              />
              {!isCollapsed && (
                <>
                  <span className="flex-1">{item.name}</span>
                  {isActive && (
                    <ChevronRight className="h-4 w-4 text-primary animate-pulse" />
                  )}
                </>
              )}
            </Link>
          )
        })}
      </nav>

      {/* Removed Presence Indicator and User Switcher */}

      {/* User Menu */}
      <div className="border-t border-border/50 p-4">
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="ghost"
              className={cn(
                "w-full gap-3 px-3 py-2 h-auto",
                isCollapsed ? "justify-center" : "justify-start"
              )}
              title={isCollapsed ? displayName : undefined}
            >
              <Avatar className="h-8 w-8 flex-shrink-0">
                <AvatarFallback className="bg-primary/20 text-primary">
                  {getInitials(displayName)}
                </AvatarFallback>
              </Avatar>
              {!isCollapsed && (
                <div className="flex flex-col items-start flex-1 min-w-0">
                  <div className="flex items-center gap-2 w-full">
                    <span className="text-sm font-medium truncate">{displayName}</span>
                    <Badge variant={isAuthenticated ? "default" : "secondary"} className="text-xs flex-shrink-0">
                      {isAuthenticated ? "Auth" : "Guest"}
                    </Badge>
                  </div>
                  <span className="text-xs text-muted-foreground truncate w-full">
                    {userId === 'aungheinaye' ? 'Developer' : userId === 'yohanna' ? 'Operations' : 'Guest User'}
                  </span>
                </div>
              )}
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-56">
            <DropdownMenuLabel>
              <div className="flex flex-col">
                <span>{displayName}</span>
                <span className="text-xs text-muted-foreground font-normal">
                  ID: {userId}
                </span>
              </div>
            </DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuItem>
              <User className="mr-2 h-4 w-4" />
              Profile
            </DropdownMenuItem>
            <DropdownMenuItem>
              <Settings className="mr-2 h-4 w-4" />
              Settings
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem 
              className="text-destructive"
              onClick={handleSignOut}
            >
              <LogOut className="mr-2 h-4 w-4" />
              Log out
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </div>
  )
}