'use client';

import React from 'react';
import { Button } from './ui/button';
import { useAppDispatch, useAppSelector } from '../store/hooks';
import { setUserFromAuth } from '../store/profileSlice';
import { Badge } from './ui/badge';
import { User } from 'lucide-react';

const DEMO_USERS = [
  { id: 'aungheinaye', name: 'Aung', email: 'aunghein<PERSON><EMAIL>' },
  { id: 'yohanna', name: 'Yohanna', email: '<EMAIL>' }
];

export const UserSwitcher: React.FC = () => {
  const dispatch = useAppDispatch();
  const { userId, displayName } = useAppSelector(state => state.profile);

  const handleUserSwitch = (userInfo: typeof DEMO_USERS[0]) => {
    // Create a mock Supabase user object
    const mockUser = {
      id: userInfo.id,
      email: userInfo.email || undefined,
      user_metadata: { display_name: userInfo.name },
      app_metadata: {},
      aud: 'authenticated',
      confirmation_sent_at: undefined,
      confirmed_at: new Date().toISOString(),
      created_at: new Date().toISOString(),
      email_confirmed_at: new Date().toISOString(),
      identities: [],
      last_sign_in_at: new Date().toISOString(),
      phone: undefined,
      recovery_sent_at: undefined,
      role: 'authenticated',
      updated_at: new Date().toISOString()
    };

    dispatch(setUserFromAuth(mockUser));
  };

  return (
    <div className="p-4 border-b border-border/50">
      <div className="flex items-center justify-between mb-3">
        <h3 className="text-sm font-medium flex items-center gap-2">
          <User className="w-4 h-4" />
          Demo User Switcher
        </h3>
        <Badge variant="outline" className="text-xs">
          Current: {userId}
        </Badge>
      </div>
      
      <div className="space-y-2">
        {DEMO_USERS.map((user) => (
          <Button
            key={user.id}
            variant={userId === user.id ? "default" : "outline"}
            size="sm"
            className="w-full justify-start"
            onClick={() => handleUserSwitch(user)}
          >
            <div className="flex flex-col items-start">
              <span className="text-sm font-medium">{user.name}</span>
              {user.email && (
                <span className="text-xs opacity-70">{user.email}</span>
              )}
            </div>
          </Button>
        ))}
      </div>
      
      <div className="mt-3 text-xs text-muted-foreground">
        Switch users to test multi-user functionality
      </div>
    </div>
  );
};