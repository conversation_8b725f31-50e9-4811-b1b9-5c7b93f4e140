import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { apiClient } from '../lib/api';
import { useAppSelector, useAppDispatch } from '../store/hooks';
import { setMemories, addMemory, updateMemory, removeMemory, setLoading, setError } from '../store/memoriesSlice';
import { toast } from 'sonner';

export const useMemoriesApi = () => {
  const dispatch = useAppDispatch();
  const queryClient = useQueryClient();
  const { userId } = useAppSelector(state => state.profile);
  const { currentPage, pageSize, filters } = useAppSelector(state => state.memories);

  // Fetch memories
  const memoriesQuery = useQuery({
    queryKey: ['memories', userId, currentPage, pageSize, filters],
    queryFn: async () => {
      console.log('Fetching memories for user:', userId);
      if (userId === 'guest') {
        return { memories: [], totalCount: 0 };
      }
      const result = await apiClient.getMemories(userId, {
        page: currentPage,
        limit: pageSize,
        search: filters.search,
        category: filters.category
      });
      console.log('API result:', result);
      return result;
    },
    enabled: userId !== 'guest',
    staleTime: 1000 * 60 * 5, // 5 minutes
  });

  // Create memory
  const createMemoryMutation = useMutation({
    mutationFn: async (memory: { title: string; content: string; metadata?: Record<string, any> }) => {
      if (userId === 'guest') {
        throw new Error('Authentication required');
      }
      return apiClient.createMemory(userId, memory);
    },
    onSuccess: (data) => {
      dispatch(addMemory(data as any));
      queryClient.invalidateQueries({ queryKey: ['memories', userId] });
      toast.success('Memory created successfully');
    },
    onError: (error) => {
      const message = error instanceof Error ? error.message : 'Failed to create memory';
      dispatch(setError(message));
      toast.error(message);
    }
  });

  // Update memory
  const updateMemoryMutation = useMutation({
    mutationFn: async ({ id, ...memory }: { id: string; title?: string; content?: string; metadata?: Record<string, any> }) => {
      if (userId === 'guest') {
        throw new Error('Authentication required');
      }
      return apiClient.updateMemory(userId, id, memory);
    },
    onSuccess: (data) => {
      dispatch(updateMemory(data as any));
      queryClient.invalidateQueries({ queryKey: ['memories', userId] });
      toast.success('Memory updated successfully');
    },
    onError: (error) => {
      const message = error instanceof Error ? error.message : 'Failed to update memory';
      dispatch(setError(message));
      toast.error(message);
    }
  });

  // Delete memory
  const deleteMemoryMutation = useMutation({
    mutationFn: async (memoryId: string) => {
      if (userId === 'guest') {
        throw new Error('Authentication required');
      }
      return apiClient.deleteMemory(userId, memoryId);
    },
    onSuccess: (_, memoryId) => {
      dispatch(removeMemory(memoryId));
      queryClient.invalidateQueries({ queryKey: ['memories', userId] });
      toast.success('Memory deleted successfully');
    },
    onError: (error) => {
      const message = error instanceof Error ? error.message : 'Failed to delete memory';
      dispatch(setError(message));
      toast.error(message);
    }
  });

  // Sync functions
  const fetchSyncCounts = async () => {
    if (userId === 'guest') {
      return { total_records: 0, total_vectors: 0, sync_status: 'unknown', timestamp: new Date().toISOString() };
    }
    
    try {
      const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5678';
      const response = await fetch(`${apiUrl}/api/v1/sync/counts`, {
        headers: {
          'X-User-ID': userId,
        },
      });
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error('Failed to fetch sync counts:', error);
      throw error;
    }
  };

  const startSync = async () => {
    if (userId === 'guest') {
      throw new Error('Authentication required');
    }
    
    try {
      const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5678';
      const response = await fetch(`${apiUrl}/api/v1/sync/start`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-User-ID': userId,
        },
      });
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error('Failed to start sync:', error);
      throw error;
    }
  };

  const getSyncStatus = async (syncId: string) => {
    try {
      const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5678';
      const response = await fetch(`${apiUrl}/api/v1/sync/status/${syncId}`, {
        headers: {
          'X-User-ID': userId,
        },
      });
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error('Failed to get sync status:', error);
      throw error;
    }
  };

  return {
    // Queries
    memories: (memoriesQuery.data as any)?.memories || [],
    totalCount: (memoriesQuery.data as any)?.totalCount || 0,
    isLoading: memoriesQuery.isLoading,
    error: memoriesQuery.error,
    
    // Mutations
    createMemory: createMemoryMutation.mutate,
    updateMemory: updateMemoryMutation.mutate,
    deleteMemory: deleteMemoryMutation.mutate,
    
    // Mutation states
    isCreating: createMemoryMutation.isPending,
    isUpdating: updateMemoryMutation.isPending,
    isDeleting: deleteMemoryMutation.isPending,
    
    // Sync functions
    fetchSyncCounts,
    startSync,
    getSyncStatus,
    
    // Refetch
    refetch: memoriesQuery.refetch
  };
};