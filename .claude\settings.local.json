{"permissions": {"allow": ["Bash(git checkout:*)", "Bash(grep:*)", "Bash(rg:*)", "Bash(python test_remote_qdrant.py:*)", "<PERSON><PERSON>(python3:*)", "Bash(rm:*)", "Bash(export:*)", "Bash(export QDRANT_PORT=6333)", "<PERSON><PERSON>(echo:*)", "<PERSON><PERSON>(timeout:*)", "<PERSON><PERSON>(python:*)", "Bash(PYTHONPATH=/home/<USER>/wsl_dev/memory-master-v2/api python3 tests/test_qdrant_connectivity_user_isolation.py)", "Bash(ls:*)", "Bash(find:*)", "<PERSON><PERSON>(docker-compose:*)", "<PERSON><PERSON>(docker builder:*)", "<PERSON><PERSON>(curl:*)", "mcp__sequential-thinking__sequentialthinking", "<PERSON><PERSON>(mkdir:*)", "Bash(npm install)", "Bash(npm run dev:*)", "mcp__playwright__browser_navigate", "Bash(ss:*)", "mcp__playwright__browser_take_screenshot", "mcp__playwright__browser_click", "mcp__playwright__browser_type", "mcp__playwright__browser_close", "Bash(git commit:*)", "Bash(git push:*)", "Bash(git config:*)", "WebFetch(domain:supabase.com)", "<PERSON><PERSON>(chmod:*)", "<PERSON><PERSON>(cat:*)", "Bash(apt list:*)", "<PERSON><PERSON>(sudo apt:*)", "Bash(sudo apt install:*)", "<PERSON><PERSON>(docker stop:*)", "<PERSON><PERSON>(true)", "Bash(./start-local-simple.sh:*)", "Bash(docker logs:*)", "<PERSON><PERSON>(docker rm:*)", "Bash(docker build:*)", "<PERSON><PERSON>(docker run:*)", "<PERSON><PERSON>(env)", "Bash(node:*)", "Bash(npm install:*)", "Bash(npx:*)", "<PERSON><PERSON>(gnome-screenshot:*)", "Bash(google-chrome:*)", "<PERSON><PERSON>(pkill:*)", "WebFetch(domain:localhost)", "<PERSON><PERSON>(sudo npx playwright:*)", "Bash(PLAYWRIGHT_SKIP_VALIDATE_HOST_REQUIREMENTS=true npx playwright install chromium)", "Bash(PLAYWRIGHT_SKIP_VALIDATE_HOST_REQUIREMENTS=true npx playwright test --reporter=html)", "Bash(npm run test:e2e:headed:*)", "Bash(npm run test:e2e:*)", "Bash(PLAYWRIGHT_SKIP_BROWSER_DOWNLOAD=0 npx playwright install chromium)", "Bash(npm init:*)", "<PERSON><PERSON>(docker restart:*)", "Bash(--name memory-api-local )", "Bash(-p 5678:8765 )", "Bash(--env-file api/.env )", "Bash(memory-api-local)", "Bash(npm test:*)", "mcp__context7__resolve-library-id", "<PERSON><PERSON>(pip show:*)"], "deny": []}, "enabledMcpjsonServers": ["supabase", "firecrawl", "desktop-commander", "context7", "sequential-thinking", "aydb-mssql", "aydb-archive-mssql", "playwright"]}